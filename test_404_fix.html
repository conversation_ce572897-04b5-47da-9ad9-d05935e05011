<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试404错误修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .warning {
            color: orange;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        #log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>测试404错误修复</h1>
    
    <div class="test-section">
        <h2>问题描述</h2>
        <p>前端正在尝试轮询不属于当前用户的任务ID：1002, 1005, 1008, 1011, 1014</p>
        <p>这些任务属于用户ID 4，但当前用户可能不是这个用户，导致404错误。</p>
    </div>

    <div class="test-section">
        <h2>测试操作</h2>
        <button onclick="testSingleTask()">测试单个任务404处理</button>
        <button onclick="testMultipleTasks()">测试多个任务404处理</button>
        <button onclick="clearLocalStorage()">清理本地存储</button>
        <button onclick="checkCurrentUser()">检查当前用户</button>
        <button onclick="clearLog()">清理日志</button>
    </div>

    <div class="test-section">
        <h2>测试日志</h2>
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testSingleTask() {
            log('开始测试单个任务404处理...');
            
            try {
                const response = await fetch('/api/convert/tasks/1002', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                });

                if (response.status === 404) {
                    log('✅ 正确收到404错误，任务不存在或无权访问', 'success');
                } else if (response.ok) {
                    const data = await response.json();
                    log(`⚠️ 意外成功获取任务数据: ${JSON.stringify(data)}`, 'warning');
                } else {
                    log(`❌ 收到其他错误: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        async function testMultipleTasks() {
            log('开始测试多个任务404处理...');
            
            const problemTaskIds = [1002, 1005, 1008, 1011, 1014];
            
            for (const taskId of problemTaskIds) {
                try {
                    const response = await fetch(`/api/convert/tasks/${taskId}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                        }
                    });

                    if (response.status === 404) {
                        log(`✅ 任务 ${taskId}: 正确收到404错误`, 'success');
                    } else if (response.ok) {
                        const data = await response.json();
                        log(`⚠️ 任务 ${taskId}: 意外成功获取数据`, 'warning');
                    } else {
                        log(`❌ 任务 ${taskId}: 收到其他错误 ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ 任务 ${taskId}: 请求失败 ${error.message}`, 'error');
                }
            }
        }

        function clearLocalStorage() {
            localStorage.clear();
            log('✅ 本地存储已清理', 'success');
        }

        async function checkCurrentUser() {
            try {
                const response = await fetch('/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                });

                if (response.ok) {
                    const user = await response.json();
                    log(`✅ 当前用户: ID=${user.id}, 邮箱=${user.email}`, 'success');
                } else {
                    log(`❌ 获取用户信息失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时自动检查当前用户
        window.onload = function() {
            log('页面加载完成，开始检查...');
            checkCurrentUser();
        };
    </script>
</body>
</html>
